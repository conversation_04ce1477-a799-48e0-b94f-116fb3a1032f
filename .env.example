# Get your API key from: https://makersuite.google.com/app/apikey

# Google AI API Key (required)
# TIP: You can provide multiple keys to effectivelly rotate & use gemini keys to avoid/minimise rate limits
GOOGLE_GEMINI_KEY=your_api_key
# GOOGLE_GEMINI_KEY1=your_api_key1
# GOOGLE_GEMINI_KEY1=your_api_key2
# GOOGLE_GEMINI_KEY1=your_api_key3


# Development Configuration (Keep the default port 6000, otherwise you'll need to manually update the MCP JSON accordingly)
PORT=6000

# OPTIONAL (Keep Defaults as they're quite good)
OUTPUT_DIR=./output
MAX_RETRY_COUNT=2
RETRY_DELAY=1000
PAGE_TIMEOUT=20000