{"name": "sniffhunt-scraper", "version": "1.0.0", "description": "SniffHunt Scraper - A comprehensive web scraper built with <PERSON><PERSON> that extracts content from web pages, including dynamic elements revealed through AI-powered interaction detection", "main": "src/server.js", "type": "module", "engines": {"node": ">=18.0.0"}, "repository": {"type": "git", "url": "https://github.com/mpmeetpatel/sniffhunt-scraper.git"}, "bugs": {"url": "https://github.com/mpmeetpatel/sniffhunt-scraper/issues"}, "homepage": "https://github.com/mpmeetpatel/sniffhunt-scraper#readme", "author": "<PERSON>", "keywords": ["web-scraper", "ai", "playwright", "markdown", "content-extraction", "automation"], "scripts": {"start": "node src/server.js", "cli": "node src/cli.js", "help-cli": "node src/cli.js --help", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write .", "format:check": "prettier --check .", "postinstall": "npx -y playwright-core install --with-deps --only-shell chromium"}, "dependencies": {"@ai-sdk/google": "^1.2.22", "@hono/node-server": "^1.17.0", "ai": "^4.3.19", "dotenv": "^17.2.0", "hono": "^4.8.5", "playwright-core": "^1.54.1", "rehype-format": "^5.0.1", "rehype-highlight": "^7.0.2", "rehype-parse": "^9.0.1", "rehype-remark": "^10.0.1", "rehype-sanitize": "^6.0.0", "rehype-stringify": "^10.0.1", "remark-gfm": "^4.0.1", "remark-stringify": "^11.0.0", "unified": "^11.0.5", "zod": "^3.24.4"}, "devDependencies": {"@eslint/js": "^9.31.0", "eslint": "^9.31.0", "eslint-config-prettier": "^10.1.8", "globals": "^16.3.0", "prettier": "^3.6.2"}}