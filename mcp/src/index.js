#!/usr/bin/env node

import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  CallToolRequestSchema,
  ListToolsRequestSchema,
} from '@modelcontextprotocol/sdk/types.js';
import { z } from 'zod';
import fetch from 'node-fetch';

const DEFAULT_BASE_URL = 'http://localhost:6000';
const SCRAPING_TIMEOUT = 300000; // 5 minutes

const ScrapeRequestSchema = z.object({
  url: z.string().url(),
  query: z.string().optional().default(''),
  mode: z.enum(['normal', 'beast']).optional().default('beast'),
  baseUrl: z.string().url().optional().default(DEFAULT_BASE_URL),
});

class SniffHuntScraperMCPServer {
  constructor() {
    this.server = new Server(
      {
        name: 'sniffhunt-scraper-mcp-server',
        version: '1.0.0',
      },
      {
        capabilities: {
          tools: {},
        },
      }
    );

    this.setupToolHandlers();
    this.setupErrorHandling();
  }

  setupToolHandlers() {
    // List available tools
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      return {
        tools: [
          {
            name: 'scrape_page',
            description: 'Scrape a web page and extract content as markdown with detailed progress tracking.',
            inputSchema: {
              type: 'object',
              properties: {
                url: {
                  type: 'string',
                  format: 'uri',
                  description: 'The URL to scrape',
                },
                query: {
                  type: 'string',
                  description: 'Optional query to focus the extraction on specific content',
                  default: '',
                },
                mode: {
                  type: 'string',
                  enum: ['normal', 'beast'],
                  description: 'Scraping mode: normal (faster) or beast (more thorough with AI)',
                  default: 'beast',
                },
                baseUrl: {
                  type: 'string',
                  format: 'uri',
                  description: 'Base URL of the scraping service',
                  default: DEFAULT_BASE_URL,
                },
              },
              required: ['url'],
            },
          },
        ],
      };
    });

    // Handle tool calls
    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;

      try {
        switch (name) {
          case 'scrape_page':
            return await this.handleScrapePageStream(args);
          default:
            throw new Error(`Unknown tool: ${name}`);
        }
      } catch (error) {
        return {
          content: [
            {
              type: 'text',
              text: `Error: ${error.message}`,
            },
          ],
          isError: true,
        };
      }
    });
  }

  async handleScrapePageStream(args) {
    const validatedArgs = ScrapeRequestSchema.parse(args);
    const { url, query, mode, baseUrl } = validatedArgs;

    const progressUpdates = [];
    let finalResult = null;

    // Enhanced progress tracking with timestamps and details
    const addProgressUpdate = (message, type = 'progress', details = null) => {
      const timestamp = new Date().toISOString().split('T')[1].split('.')[0];
      const emoji = type === 'error' ? '❌' : type === 'success' ? '✅' : type === 'info' ? 'ℹ️' : '⚡';

      let progressEntry = `${emoji} **${timestamp}** - ${message}`;
      if (details) {
        progressEntry += `\n   ${details}`;
      }

      progressUpdates.push(progressEntry);

      // Log to console for immediate feedback during development
      console.log(`[MCP] ${emoji} ${message}${details ? ` (${details})` : ''}`);
    };

    try {
      addProgressUpdate('Connecting to scraping service...', 'info');

      // Use fetch with streaming response
      const response = await fetch(`${baseUrl}/scrape`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'text/event-stream',
        },
        body: JSON.stringify({ url, query, mode }),
      });

      addProgressUpdate('Connected to scraping service, starting process...', 'info');

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      // Set up timeout
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => {
          reject(new Error('Scraping timeout'));
        }, SCRAPING_TIMEOUT);
      });

      // Process the streaming response using async iteration
      let buffer = '';

      const streamingPromise = (async () => {
        // Use async iteration over the response body
        for await (const chunk of response.body) {
          buffer += chunk.toString();

          // Process complete SSE messages
          const lines = buffer.split('\n');
          buffer = lines.pop() || ''; // Keep incomplete line in buffer

          for (const line of lines) {
            if (line.trim() === '') continue;

            try {
              if (line.startsWith('data: ')) {
                const data = line.slice(6);
                if (data === '[DONE]') {
                  addProgressUpdate('Scraping process completed', 'success');
                  break;
                }

                const parsed = JSON.parse(data);

                if (parsed.type === 'progress') {
                  addProgressUpdate(parsed.message, 'progress', parsed.details);
                } else if (parsed.type === 'completed') {
                  // Handle the 'completed' response type from the server
                  finalResult = parsed;
                  if (parsed.result?.success && parsed.result?.data?.markdown) {
                    addProgressUpdate('Final result received', 'success', `Content: ${parsed.result.data.markdown.length} chars`);
                  } else {
                    addProgressUpdate('Scraping completed but no content extracted', 'error', parsed.result?.error || 'Unknown error');
                  }
                } else if (parsed.type === 'result') {
                  // Legacy support for 'result' type
                  finalResult = parsed;
                  addProgressUpdate('Final result received', 'success', `Content: ${finalResult.data?.markdown?.length || 0} chars`);
                } else if (parsed.type === 'error') {
                  addProgressUpdate(parsed.message, 'error', parsed.details);
                  break;
                } else if (parsed.type === 'done') {
                  addProgressUpdate('Scraping process completed', 'success');
                  break;
                }
              }
            } catch (parseError) {
              addProgressUpdate(`Parse error: ${parseError.message}`, 'error');
            }
          }
        }
      })();

      // Race between streaming and timeout
      await Promise.race([streamingPromise, timeoutPromise]);

      // Create progress timeline
      const progressTimeline = progressUpdates.join('\n');
      const totalSteps = progressUpdates.length;
      const successSteps = progressUpdates.filter(update => update.includes('✅')).length;
      const errorSteps = progressUpdates.filter(update => update.includes('❌')).length;

      // Check if we have any successful result data
      let resultData = null;
      let success = false;

      if (finalResult) {
        // Handle 'completed' response type
        if (finalResult.type === 'completed' && finalResult.result) {
          resultData = finalResult.result.data;
          success = finalResult.result.success;
        }
        // Handle legacy 'result' type
        else if (finalResult.data) {
          resultData = finalResult.data;
          success = finalResult.success;
        }
      }

      if (success && resultData?.markdown) {
        const processingTime = resultData.metadata?.processingTime || 0;
        const contentLength = resultData.markdown.length;

        return {
          content: [
            {
              type: 'text',
              text: `# ✅ Scraping Results for ${url}

## 📊 Summary
- **Status**: Success ✅
- **Content Length**: ${contentLength.toLocaleString()} characters
- **Processing Time**: ${Math.round(processingTime / 1000)}s
- **Total Steps**: ${totalSteps} (${successSteps} successful, ${errorSteps} errors)
- **Mode**: ${mode}
- **Query**: ${query || 'None'}

## 📋 Progress Timeline
${progressTimeline}

## 📄 Extracted Content

${resultData.markdown}`,
            },
          ],
        };
      } else {
        // Handle failure case with detailed progress
        const errorMessage = finalResult?.result?.error || finalResult?.error || 'No data was extracted';
        throw new Error(`Scraping failed. ## Progress Log\n${progressTimeline}\n\n**Final Error:** ${errorMessage}`);
      }

    } catch (error) {
      throw new Error(`Failed to start streaming scrape: ${error.message}`);
    }
  }

  setupErrorHandling() {
    this.server.onerror = (error) => {
      console.error('[MCP Error]', error);
    };

    process.on('SIGINT', async () => {
      await this.server.close();
      process.exit(0);
    });
  }

  async run() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error('SniffHunt Scraper MCP server running on stdio');
  }
}

// Start the server
const server = new SniffHuntScraperMCPServer();
server.run().catch((error) => {
  console.error('Failed to start server:', error);
  process.exit(1);
});
