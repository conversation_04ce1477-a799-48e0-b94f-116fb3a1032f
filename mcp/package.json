{"name": "sniffhunt-scraper-mcp-server", "version": "1.0.0", "description": "SniffHunt MCP Server - A Model Context Protocol (MCP) server for web scraping and content extraction or URL to markdown conversion.", "type": "module", "main": "src/index.js", "engines": {"node": ">=18.0.0"}, "scripts": {"start": "node src/index.js", "dev": "node --watch src/index.js", "build": "echo 'No build step required'", "test": "node test/test-server.js"}, "keywords": ["mcp", "model-context-protocol", "web-scraping", "streaming", "sse"], "author": "", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^1.16.0", "node-fetch": "^3.3.2", "zod": "^3.24.4"}, "devDependencies": {"@types/node": "^24.0.15"}, "mcp": {"server": {"name": "sniffhunt-scraper-mcp-server", "version": "1.0.0"}}}