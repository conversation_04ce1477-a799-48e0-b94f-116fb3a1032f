#!/usr/bin/env node

import { fileURLToPath } from 'url';
import { dirname, join, resolve } from 'path';
import { platform, homedir } from 'os';
import { existsSync } from 'fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

/**
 * Generate MCP server configuration for different platforms and tools
 */
function generateMCPConfig() {
  const projectRoot = resolve(__dirname, '..');
  const mcpServerPath = join(projectRoot, 'mcp', 'src', 'index.js');
  
  // Verify MCP server exists
  if (!existsSync(mcpServerPath)) {
    console.error('❌ MCP server not found at:', mcpServerPath);
    console.error('Please ensure the MCP server is properly installed.');
    process.exit(1);
  }

  const currentPlatform = platform();
  const configs = {};

  // Claude Desktop Configuration
  const claudeConfigPath = getClaudeConfigPath(currentPlatform);
  configs.claude = {
    name: '<PERSON>',
    configPath: claudeConfigPath,
    config: {
      mcpServers: {
        'sniffhunt-scraper': {
          command: 'node',
          args: [mcpServerPath],
          env: {
            NODE_ENV: 'production'
          }
        }
      }
    }
  };

  // Continue Desktop Configuration  
  const continueConfigPath = getContinueConfigPath(currentPlatform);
  configs.continue = {
    name: 'Continue (VS Code Extension)',
    configPath: continueConfigPath,
    config: {
      mcpServers: {
        'sniffhunt-scraper': {
          command: 'node',
          args: [mcpServerPath]
        }
      }
    }
  };

  // Cline Configuration
  configs.cline = {
    name: 'Cline (VS Code Extension)',
    note: 'Add to VS Code settings.json under "cline.mcpServers"',
    config: {
      'sniffhunt-scraper': {
        command: 'node',
        args: [mcpServerPath]
      }
    }
  };

  // Generic MCP Configuration
  configs.generic = {
    name: 'Generic MCP Client',
    config: {
      mcpServers: {
        'sniffhunt-scraper': {
          command: 'node',
          args: [mcpServerPath],
          description: 'SniffHunt Scraper - AI-powered web scraping with streaming progress'
        }
      }
    }
  };

  return { configs, projectRoot, mcpServerPath };
}

/**
 * Get Claude Desktop config path based on platform
 */
function getClaudeConfigPath(platform) {
  switch (platform) {
    case 'darwin': // macOS
      return join(homedir(), 'Library', 'Application Support', 'Claude', 'claude_desktop_config.json');
    case 'win32': // Windows
      return join(homedir(), 'AppData', 'Roaming', 'Claude', 'claude_desktop_config.json');
    case 'linux':
      return join(homedir(), '.config', 'claude', 'claude_desktop_config.json');
    default:
      return join(homedir(), '.config', 'claude', 'claude_desktop_config.json');
  }
}

/**
 * Get Continue config path based on platform
 */
function getContinueConfigPath(platform) {
  switch (platform) {
    case 'darwin': // macOS
      return join(homedir(), '.continue', 'config.json');
    case 'win32': // Windows
      return join(homedir(), '.continue', 'config.json');
    case 'linux':
      return join(homedir(), '.continue', 'config.json');
    default:
      return join(homedir(), '.continue', 'config.json');
  }
}

/**
 * Display configuration information
 */
function displayConfigs() {
  const { configs, projectRoot, mcpServerPath } = generateMCPConfig();
  const currentPlatform = platform();
  
  console.log('\n🚀 SniffHunt Scraper MCP Configuration Generator\n');
  console.log(`📍 Platform: ${currentPlatform}`);
  console.log(`📂 Project Root: ${projectRoot}`);
  console.log(`🔧 MCP Server: ${mcpServerPath}\n`);

  console.log('=' .repeat(80));
  console.log('📋 COPY-PASTE CONFIGURATIONS FOR MCP TOOLS');
  console.log('=' .repeat(80));

  // Claude Desktop Configuration
  console.log(`\n🤖 ${configs.claude.name}`);
  console.log(`📁 Config Location: ${configs.claude.configPath}`);
  console.log('📄 Configuration to add/merge:');
  console.log('```json');
  console.log(JSON.stringify(configs.claude.config, null, 2));
  console.log('```\n');

  // Continue Configuration
  console.log(`🔄 ${configs.continue.name}`);
  console.log(`📁 Config Location: ${configs.continue.configPath}`);
  console.log('📄 Configuration to add/merge:');
  console.log('```json');
  console.log(JSON.stringify(configs.continue.config, null, 2));
  console.log('```\n');

  // Cline Configuration
  console.log(`🎯 ${configs.cline.name}`);
  console.log(`📝 ${configs.cline.note}`);
  console.log('📄 Configuration to add:');
  console.log('```json');
  console.log(JSON.stringify(configs.cline.config, null, 2));
  console.log('```\n');

  // Generic Configuration
  console.log(`⚙️  ${configs.generic.name}`);
  console.log('📄 Standard MCP configuration:');
  console.log('```json');
  console.log(JSON.stringify(configs.generic.config, null, 2));
  console.log('```\n');

  console.log('=' .repeat(80));
  console.log('🎯 USAGE INSTRUCTIONS');
  console.log('=' .repeat(80));
  console.log(`
1. 🚀 Start the scraper server first:
   npm start

2. 📋 Copy the appropriate configuration above
3. 📝 Paste it into your MCP tool's config file
4. 🔄 Restart your MCP tool
5. ✅ Use the 'scrape_page' tool in your MCP client

🔧 Tool Usage:
- URL: The webpage to scrape
- Mode: 'normal' (fast) or 'beast' (AI-powered interactive)
- Query: Optional - specific content to extract
- Base URL: Optional - custom scraper service URL (default: http://localhost:6000)

📖 Example:
"Scrape https://docs.react.dev using beast mode and extract information about hooks"
`);

  console.log('=' .repeat(80));
  console.log('🆘 TROUBLESHOOTING');
  console.log('=' .repeat(80));
  console.log(`
❌ If MCP tool can't find the server:
   - Ensure Node.js is in your PATH
   - Use absolute path: ${mcpServerPath}
   - Check file permissions

❌ If scraping fails:
   - Ensure the main server is running (npm start)
   - Check your Google AI API key in .env
   - Verify network connectivity

❌ If config doesn't work:
   - Restart your MCP tool after config changes
   - Check config file syntax (valid JSON)
   - Verify file paths are correct
`);
}

// Run the configuration generator
if (import.meta.url === `file://${process.argv[1]}`) {
  displayConfigs();
}

export { generateMCPConfig, displayConfigs };
