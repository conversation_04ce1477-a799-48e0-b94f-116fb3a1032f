# 🚀 SniffHunt Scraper - Advanced AI-Powered Web Scraper
**Transform any URL into clean, LLM-ready markdown with AI-powered interactive element detection and human-like browsing automation.**

## 🏆 Why SniffHunt Scraper Outperforms Paid Competitors

### **Revolutionary AI DOM Analysis**
- 🧠 **AI analyzes page structure like a human** - Deep understanding of content relationships
- 🎯 **Automatically identifies and interacts with hidden elements** - Finds tabs, accordions, modals
- 📊 **Contextual content understanding** - Prioritizes important information based on page type

### **True Interactive Element Mastery**
- 🖱️ **Clicks tabs, dropdowns, accordions, modals automatically** - No manual intervention needed
- 🔄 **Handles lazy-loading and infinite scroll** - Captures all dynamic content
- 🕵️ **Extracts content hidden behind user interactions** - Reveals previously inaccessible data

### **Advanced JavaScript & SPA Support**
- ⚛️ **Full React, Vue, Angular support** - Renders SPAs completely
- 🖼️ **Iframe content processing** - Extracts embedded content seamlessly
- 🚀 **Dynamic content extraction** - Waits for all network requests to complete

### **Natural Language Query Processing**
- 💬 **Extract specific content using plain English** - "Find pricing information"
- 🎛️ **Context-aware content filtering** - Focuses on relevant sections
- 🏷️ **Smart content prioritization** - Based on user intent and page structure


## ⚡ Quick Start

### 1. Install
```bash
git clone https://github.com/mpmeetpatel/sniffhunt-scraper.git
cd sniffhunt-scraper
npm install
echo "GOOGLE_AI_API_KEY=your_api_key_here" > .env
```

### 2. Start Server
```bash
npm start
# Server runs on http://localhost:6000
```

### 3. Extract Content (See Server Logs for live process watching)

---

## 🧠 Modes Explained - Deep Dive

### 🚀 Normal Mode - Fast & Intelligent Extraction

**What it does:**
Normal mode is designed for fast, efficient extraction of web content with intelligent handling of modern web technologies. It's perfect for most websites and provides excellent results without the complexity of interactive element automation.

**Technical Process:**
1. **Page Loading & Rendering**: Loads the full page and waits for JavaScript execution
2. **Infinite Scroll Handling**: Automatically scrolls to trigger lazy-loading content
3. **SPA Support**: Waits for React, Vue, Angular apps to fully render
4. **Iframe Processing**: Extracts content from embedded iframes
5. **Content Cleanup**: Removes scripts, styles, and non-content elements
6. **AI Optimization**: Uses AI to convert HTML to clean, structured markdown
7. **Query Processing**: If user query provided, AI focuses on relevant content

**What Normal Mode Handles:**
- ✅ **Single Page Applications (SPAs)**: React, Vue, Angular, Svelte apps
- ✅ **Lazy Loading**: Infinite scroll, progressive loading
- ✅ **JavaScript Rendering**: Dynamic content generation
- ✅ **Iframes**: Embedded content (videos, maps, widgets)
- ✅ **AJAX Content**: Content loaded after initial page load
- ✅ **CSS-based Layouts**: Complex styling and positioning
- ✅ **Static Interactive Elements**: Content already in DOM but hidden with CSS

**What Normal Mode CANNOT Handle:**
- ❌ **Dynamic Interactive Elements**: Tabs that load content via AJAX
- ❌ **Modal Content**: Content fetched when modals are opened
- ❌ **Hidden Form Data**: Content revealed after form interactions
- ❌ **Progressive Disclosure**: Content that appears only after user actions

**Perfect For:**
- 📰 **News Websites**: Articles, blog posts, news content
- 📚 **Documentation**: API docs, tutorials, guides (non-interactive)
- 🛍️ **E-commerce**: Product pages with static information
- 🏢 **Corporate Sites**: Landing pages, about pages, services
- 📖 **Content Sites**: Wikis, educational content, resources
- 🎥 **Media Sites**: Video descriptions, podcast transcripts

**Examples:**
```bash
# Extract full Wikipedia article
curl -X POST http://localhost:6000/scrape \
  -H "Content-Type: application/json" \
  -d '{"url": "https://en.wikipedia.org/wiki/Machine_learning", "mode": "normal"}'

# Extract specific content from documentation
curl -X POST http://localhost:6000/scrape \
  -H "Content-Type: application/json" \
  -d '{"url": "https://docs.python.org/3/tutorial/", "mode": "normal", "query": "data structures and functions"}'

# Extract news article
curl -X POST http://localhost:6000/scrape \
  -H "Content-Type: application/json" \
  -d '{"url": "https://techcrunch.com/latest-ai-news", "mode": "normal"}'
```

---

### 🦁 Beast Mode - Ultimate AI-Powered Interactive Extraction

**What it does:**
Beast mode combines everything from Normal mode with advanced AI-powered browser automation that can detect, interact with, and extract content from complex interactive elements. It browses websites like a human user would.

**Technical Process:**
1. **All Normal Mode Steps** (page loading, rendering, infinite scroll, etc.)
2. **AI DOM Analysis**: Deep analysis of page structure to understand element relationships
3. **Interactive Element Detection**: AI identifies clickable elements that might reveal hidden content
4. **Element Classification**: Categorizes elements by interaction type (click, hover, input)
5. **Smart Interaction**: Performs human-like interactions with detected elements
6. **Dynamic Content Extraction**: Captures content revealed by interactions
7. **Modal & Popup Handling**: Manages overlay content and dismisses modals
8. **Content Combination**: Merges static and dynamically revealed content
9. **AI-Enhanced Processing**: Advanced content prioritization and optimization

**Advanced Features:**
- 🧠 **AI DOM Understanding**: Analyzes page structure like a human developer
- 🖱️ **Human-like Interactions**: Clicks, hovers, scrolls with realistic timing
- 🔍 **Hidden Content Discovery**: Finds content behind tabs, accordions, modals
- 🎭 **Modal Management**: Opens, extracts from, and closes modal dialogs
- 🎯 **Context-Aware Filtering**: Understands page type and prioritizes relevant interactions
- 🚫 **Smart Exclusions**: Avoids navigation, authentication, and destructive actions
- 🔄 **State Management**: Handles complex UI state changes and transitions

**What Beast Mode Handles (Everything from Normal Mode PLUS):**
- ✅ **Dynamic Tabs**: Tabs that load content via AJAX when clicked
- ✅ **Expandable Accordions**: Content fetched when sections are expanded
- ✅ **Interactive Modals**: Content loaded when modals/dialogs are opened
- ✅ **Dropdown Menus**: Content revealed in dropdown selections
- ✅ **Progressive Disclosure**: Content that appears after user interactions
- ✅ **Filtered Views**: Content shown after applying filters or toggles
- ✅ **Code Example Toggles**: Programming examples shown on button clicks
- ✅ **Hidden Features**: Functionality revealed through UI exploration

**Detailed Examples:**

```bash
# Extract all button variations and code examples from Material-UI
curl -X POST http://localhost:6000/scrape \
  -H "Content-Type: application/json" \
  -d '{
    "url": "https://mui.com/material-ui/react-button/", 
    "mode": "beast",
    "query": "button variants, sizes, colors, and code examples"
  }'
```

```bash
# Extract hidden analytics and reports from a dashboard
curl -X POST http://localhost:6000/scrape \
  -H "Content-Type: application/json" \
  -d '{
    "url": "https://analytics.google.com/analytics/web/", 
    "mode": "beast",
    "query": "traffic reports, user behavior, and conversion metrics"
  }'
```

```bash
# Extract complete product information including hidden details
curl -X POST http://localhost:6000/scrape \
  -H "Content-Type: application/json" \
  -d '{
    "url": "https://www.apple.com/macbook-pro/", 
    "mode": "beast",
    "query": "technical specifications, features, and pricing details"
  }'
```

```bash
# Extract complete API documentation with examples
curl -X POST http://localhost:6000/scrape \
  -H "Content-Type: application/json" \
  -d '{
    "url": "https://docs.stripe.com/api", 
    "mode": "beast",
    "query": "API endpoints, parameters, and code examples"
  }'
```


## 🔗 MCP Server

### Setup

1. **Configure MCP JSON in your app**:
```json
{
  "mcpServers": {
    "sniffhunt-scraper": {
      "command": "node",
      "args": ["/path/to/sniffhunt-scraper/src/server.js"]
    }
  }
}
```

2. **Usage Examples in MCP**

**Normal Mode:**
```
Scrape https://docs.react.dev in normal mode and extract the full documentation.
```

```
Extract from https://stripe.com/pricing in normal mode, focusing on "pricing tiers and features".
```

**Beast Mode:**
```
Scrape https://ui.shadcn.com/docs/components/button using beast mode to get all interactive examples.
```

```
Extract from https://dashboard.stripe.com in beast mode, looking for "payment analytics and reports".
```

**Benefits:**
- Direct integration with any MCP supported applications
- Scraped content becomes part of chat context  
- Ask follow-up questions about extracted content
- No need to copy/paste results


## 💻 CLI Usage

```bash
# Simple extraction
npm run cli https://example.com

# With options
npm run cli https://docs.react.dev -q "hooks examples" -m beast -o react-hooks

# Real examples
npm run cli https://tailwindcss.com/docs -q "responsive design" -m normal
npm run cli https://www.notion.so/templates -m beast -o notion-templates
```

**Options:**
- `-m, --mode`: `normal` (fast) or `beast` (interactive) 
- `-q, --query`: Extract specific content
- `-o, --output`: Custom filename

---

## 🔧 Configuration

**Required:**
- Node.js 18+
- Google AI API Key ([Get one here](https://makersuite.google.com/app/apikey))

**Optional Environment Variables:**
```env
OUTPUT_DIR=./output
MAX_RETRY_COUNT=2
RETRY_DELAY=1000
PAGE_TIMEOUT=20000
PORT=6000
```

---

## 🎯 Common Use Cases

**Documentation Sites:**
```bash
curl -X POST http://localhost:6000/scrape-sync \
  -H "Content-Type: application/json" \
  -d '{"url": "https://kubernetes.io/docs", "mode": "beast", "query": "pod and service concepts"}'
```

**Component Libraries:**
```bash
curl -X POST http://localhost:6000/scrape-sync \
  -H "Content-Type: application/json" \
  -d '{"url": "https://chakra-ui.com/docs/components", "mode": "beast"}'
```

**E-commerce:**
```bash
curl -X POST http://localhost:6000/scrape-sync \
  -H "Content-Type: application/json" \
  -d '{"url": "https://www.apple.com/macbook-pro", "mode": "beast", "query": "specifications and pricing"}'
```

**News & Blogs:**
```bash
curl -X POST http://localhost:6000/scrape-sync \
  -H "Content-Type: application/json" \
  -d '{"url": "https://techcrunch.com", "mode": "normal", "query": "AI and technology news"}'
```

---

## 🚨 Troubleshooting

**Browser Issues:**
```bash
# Manual browser install if needed
npx -y playwright-core install --with-deps --only-shell chromium
```

**API Errors:**
- Check your Google AI API key is valid
- Ensure you have sufficient API quota

**MCP Not Working:**
- Verify the path in MCP config is correct
- Check server is running or not

---

## 📡 API Reference

**POST /scrape-sync** (Simple)
```json
{
  "url": "https://example.com",
  "mode": "normal|beast",
  "query": "optional specific content"
}
```

**POST /scrape** (Streaming)
- Same payload as above
- Returns Server-Sent Events with progress

**GET /health**
- Check server status and API key

---

## 🤝 Support

- 🐛 [Report Issues](https://github.com/mpmeetpatel/sniffhunt-scraper/issues)
- ⭐ [Star the repo](https://github.com/mpmeetpatel/sniffhunt-scraper) if it helps!

**Made with ❤️ for developers who need smart web scraping**